/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.service;

import com.izpan.modules.base.domain.dto.payment.WechatCallbackDTO;
import com.izpan.modules.base.domain.dto.payment.WechatPayOrderDTO;
import com.izpan.modules.base.domain.dto.payment.WechatRefundDTO;
import com.izpan.modules.base.domain.vo.WechatPayResultVO;

import java.util.Map;

/**
 * 微信支付服务接口
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.service.IWechatPayService
 * @CreateTime 2025-07-20
 */
public interface IWechatPayService {

    /**
     * 创建支付订单
     *
     * @param wechatPayOrderDTO 支付订单信息
     * @return 支付结果
     */
    WechatPayResultVO createPayOrder(WechatPayOrderDTO wechatPayOrderDTO);

    /**
     * 查询订单支付状态
     *
     * @param orderNo 商户订单号
     * @return 支付状态
     */
    String queryOrderStatus(String orderNo);

    /**
     * 处理支付回调
     *
     * @param callbackData 回调数据Map
     * @return 处理结果
     */
    boolean handlePayCallback(Map<String, Object> callbackData);

    /**
     * 申请退款
     *
     * @param wechatRefundDTO 退款信息
     * @return 退款结果
     */
    boolean applyRefund(WechatRefundDTO wechatRefundDTO);

    /**
     * 查询退款状态
     *
     * @param refundNo 商户退款单号
     * @return 退款状态
     */
    String queryRefundStatus(String refundNo);

    /**
     * 更新订单支付状态（定时任务调用）
     *
     * @param orderNo 商户订单号
     * @return 更新结果
     */
    boolean updateOrderPayStatus(String orderNo);

}
