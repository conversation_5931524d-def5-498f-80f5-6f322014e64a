/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.google.gson.Gson;
import com.izpan.infrastructure.config.WechatPayProperties;
import com.izpan.infrastructure.config.WechatPayV3ClientFactory;
import com.izpan.infrastructure.constants.WechatPayConstants;
import com.izpan.modules.base.domain.dto.payment.WechatCallbackDTO;
import com.izpan.modules.base.domain.dto.payment.WechatPayOrderDTO;
import com.izpan.modules.base.domain.dto.payment.WechatRefundDTO;
import com.izpan.modules.base.domain.entity.BsePaymentOrder;
import com.izpan.modules.base.domain.vo.WechatPayResultVO;
import com.izpan.modules.base.service.IBsePaymentOrderService;
import com.izpan.modules.base.service.IWechatPayService;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import com.wechat.pay.java.service.payments.jsapi.JsapiServiceExtension;
import com.wechat.pay.java.service.payments.jsapi.model.*;
import com.wechat.pay.java.service.payments.jsapi.model.Amount;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信支付服务实现类
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.service.impl.WechatPayServiceImpl
 * @CreateTime 2025-07-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WechatPayServiceImpl implements IWechatPayService {

    private final WechatPayProperties wechatPayProperties;
    private final IBsePaymentOrderService bsePaymentOrderService;
    private final WechatPayV3ClientFactory wechatPayV3ClientFactory;

    @Override
    @Transactional
    public WechatPayResultVO createPayOrder(WechatPayOrderDTO wechatPayOrderDTO) {
        WechatPayResultVO result = new WechatPayResultVO();
        
        try {
            // 1. 生成商户订单号
            String orderNo = generateOrderNo();
            
            // 2. 创建支付订单记录
            BsePaymentOrder paymentOrder = createPaymentOrderRecord(wechatPayOrderDTO, orderNo);
            Long orderId = paymentOrder.getId();
            
            // 3. 调用微信统一下单接口并生成支付参数 (APIv3)
            PrepayWithRequestPaymentResponse prepayResponse = callWechatUnifiedOrderV3(wechatPayOrderDTO, orderNo);

            if (prepayResponse == null) {
                result.setStatus(WechatPayConstants.ORDER_STATUS_FAILED);
                result.setErrorMsg("调用微信支付接口失败");
                return result;
            }

            // 4. 获取支付参数
            Map<String, Object> payParams = convertToPayParams(prepayResponse);
            
            // 5. 返回结果
            result.setOrderNo(orderNo);
            result.setOrderId(orderId);
            result.setPayParams(payParams);
            result.setStatus(WechatPayConstants.ORDER_STATUS_PENDING);
            
            log.info("创建支付订单成功，订单号：{}", orderNo);
            
        } catch (Exception e) {
            log.error("创建支付订单失败", e);
            result.setStatus(WechatPayConstants.ORDER_STATUS_FAILED);
            result.setErrorMsg("创建支付订单失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    public String queryOrderStatus(String orderNo) {
        try {
            JsapiService jsapiService = wechatPayV3ClientFactory.getJsapiService();

            // 构建查询请求
            QueryOrderByOutTradeNoRequest request = new QueryOrderByOutTradeNoRequest();
            request.setMchid(wechatPayProperties.getMchId());
            request.setOutTradeNo(orderNo);

            // 执行查询
            Transaction transaction = jsapiService.queryOrderByOutTradeNo(request);

            if (transaction != null) {
                log.info("查询订单状态成功，订单号：{}，状态：{}", orderNo, transaction.getTradeState());
                return convertWechatStatusToOrderStatus(transaction.getTradeState().name());
            }

        } catch (ServiceException e) {
            log.error("查询订单状态失败，订单号：{}，错误码：{}，错误信息：{}", orderNo, e.getErrorCode(), e.getErrorMessage());
        } catch (Exception e) {
            log.error("查询订单状态失败，订单号：{}", orderNo, e);
        }

        return WechatPayConstants.ORDER_STATUS_FAILED;
    }

    /**
     * 生成商户订单号
     */
    private String generateOrderNo() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        return "WX" + LocalDateTime.now().format(formatter) + IdUtil.randomUUID().substring(0, 8);
    }

    /**
     * 创建支付订单记录
     */
    private BsePaymentOrder createPaymentOrderRecord(WechatPayOrderDTO dto, String orderNo) {
        BsePaymentOrder paymentOrder = new BsePaymentOrder();
        paymentOrder.setOrderNo(orderNo);
        paymentOrder.setUserId(dto.getUserId());
        paymentOrder.setPackageId(dto.getPackageId());
        paymentOrder.setPackageSnapshot(dto.getPackageSnapshot());
        paymentOrder.setAmountPayable(dto.getAmountPayable());
        paymentOrder.setStatus(WechatPayConstants.ORDER_STATUS_PENDING);
        paymentOrder.setPaymentChannel(WechatPayConstants.PAYMENT_CHANNEL_WECHAT_LITE);
        paymentOrder.setRefundStatus(WechatPayConstants.REFUND_STATUS_NO_REFUND);
        paymentOrder.setExpiresAt(LocalDateTime.now().plusMinutes(dto.getExpireMinutes()));
        
        bsePaymentOrderService.save(paymentOrder);
        return paymentOrder;
    }

    /**
     * 调用微信统一下单接口并生成支付参数 (APIv3)
     */
    private PrepayWithRequestPaymentResponse callWechatUnifiedOrderV3(WechatPayOrderDTO dto, String orderNo) {
        try {
            JsapiServiceExtension jsapiServiceExtension = wechatPayV3ClientFactory.getJsapiServiceExtension();

            // 构建请求对象
            PrepayRequest request = new PrepayRequest();
            request.setAppid(wechatPayProperties.getAppId());
            request.setMchid(wechatPayProperties.getMchId());
            request.setDescription(dto.getBody());
            request.setOutTradeNo(orderNo);
            request.setNotifyUrl(wechatPayProperties.getNotifyUrl());

            // 设置金额
            Amount amount = new Amount();
            amount.setTotal(dto.getAmountPayable());
            amount.setCurrency("CNY");
            request.setAmount(amount);

            // 设置支付者
            Payer payer = new Payer();
            payer.setOpenid(dto.getOpenid());
            request.setPayer(payer);

            // 执行请求，直接获取包含支付参数的响应
            PrepayWithRequestPaymentResponse response = jsapiServiceExtension.prepayWithRequestPayment(request);
            log.info("微信支付下单成功");
            return response;

        } catch (ServiceException e) {
            log.error("微信支付下单失败，错误码：{}，错误信息：{}", e.getErrorCode(), e.getErrorMessage());
            return null;
        } catch (Exception e) {
            log.error("调用微信统一下单接口失败", e);
            return null;
        }
    }

    /**
     * 转换支付参数
     */
    private Map<String, Object> convertToPayParams(PrepayWithRequestPaymentResponse response) {
        Map<String, Object> result = new HashMap<>();
        result.put("appId", response.getAppId());
        result.put("timeStamp", response.getTimeStamp());
        result.put("nonceStr", response.getNonceStr());
        result.put("package", response.getPackageVal());
        result.put("signType", response.getSignType());
        result.put("paySign", response.getPaySign());
        return result;
    }

    /**
     * 转换微信支付状态为订单状态
     */
    private String convertWechatStatusToOrderStatus(String wechatStatus) {
        return switch (wechatStatus) {
            case "SUCCESS" -> WechatPayConstants.ORDER_STATUS_PAID;
            case "CLOSED" -> WechatPayConstants.ORDER_STATUS_CLOSED;
            case "PAYERROR" -> WechatPayConstants.ORDER_STATUS_FAILED;
            default -> WechatPayConstants.ORDER_STATUS_PENDING;
        };
    }

    @Override
    @Transactional
    public boolean handlePayCallback(Map<String, Object> callbackData) {
        try {
            log.info("开始处理微信支付回调，数据：{}", callbackData);

            // 1. 验证签名 (APIv3 使用新的验签方式，这里暂时跳过)
            // TODO: 使用 APIv3 的 NotificationParser 进行验签
            log.info("APIv3 回调验签已在 Controller 层处理");

            // 2. 检查支付结果
            String returnCode = (String) callbackData.get("return_code");
            String resultCode = (String) callbackData.get("result_code");

            if (!WechatPayConstants.WECHAT_PAY_SUCCESS.equals(returnCode) ||
                !WechatPayConstants.WECHAT_PAY_SUCCESS.equals(resultCode)) {
                log.error("微信支付回调失败，return_code：{}，result_code：{}，错误信息：{}",
                    returnCode, resultCode, callbackData.get("err_code_des"));
                return false;
            }

            // 3. 更新订单状态
            return updateOrderAfterPayment(callbackData);

        } catch (Exception e) {
            log.error("处理微信支付回调失败", e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean applyRefund(WechatRefundDTO wechatRefundDTO) {
        try {
            // 1. 查询订单信息
            BsePaymentOrder paymentOrder = bsePaymentOrderService.getById(wechatRefundDTO.getOrderId());
            if (paymentOrder == null || !WechatPayConstants.ORDER_STATUS_PAID.equals(paymentOrder.getStatus())) {
                log.error("订单不存在或状态不正确，无法退款");
                return false;
            }

            // 2. 生成退款单号
            String refundNo = generateRefundNo();

            // 3. 调用微信退款接口 (APIv3)
            boolean refundResult = callWechatRefundV3(paymentOrder, wechatRefundDTO, refundNo);

            if (refundResult) {
                // 4. 更新订单退款状态
                paymentOrder.setRefundStatus(WechatPayConstants.REFUND_STATUS_PENDING);
                paymentOrder.setRefundNo(refundNo);
                paymentOrder.setRefundAmount(wechatRefundDTO.getRefundAmount());
                bsePaymentOrderService.updateById(paymentOrder);
            }

            return refundResult;

        } catch (Exception e) {
            log.error("申请退款失败", e);
            return false;
        }
    }

    @Override
    public String queryRefundStatus(String refundNo) {
        try {
            RefundService refundService = wechatPayV3ClientFactory.getRefundService();

            // 构建查询请求
            QueryByOutRefundNoRequest request = new QueryByOutRefundNoRequest();
            request.setOutRefundNo(refundNo);

            // 执行查询
            Refund refund = refundService.queryByOutRefundNo(request);

            if (refund != null) {
                log.info("查询退款状态成功，退款单号：{}，状态：{}", refundNo, refund.getStatus());
                return convertWechatRefundStatusToOrderStatus(refund.getStatus().name());
            }

        } catch (ServiceException e) {
            log.error("查询退款状态失败，退款单号：{}，错误码：{}，错误信息：{}", refundNo, e.getErrorCode(), e.getErrorMessage());
        } catch (Exception e) {
            log.error("查询退款状态失败，退款单号：{}", refundNo, e);
        }

        return WechatPayConstants.REFUND_STATUS_FAILED;
    }

    @Override
    @Transactional
    public boolean updateOrderPayStatus(String orderNo) {
        try {
            String payStatus = queryOrderStatus(orderNo);

            if (WechatPayConstants.ORDER_STATUS_PAID.equals(payStatus)) {
                // 查询订单并更新状态
                BsePaymentOrder paymentOrder = bsePaymentOrderService.lambdaQuery()
                    .eq(BsePaymentOrder::getOrderNo, orderNo)
                    .one();

                if (paymentOrder != null && WechatPayConstants.ORDER_STATUS_PENDING.equals(paymentOrder.getStatus())) {
                    paymentOrder.setStatus(WechatPayConstants.ORDER_STATUS_PAID);
                    paymentOrder.setPaidAt(LocalDateTime.now());
                    paymentOrder.setAmountPaid(paymentOrder.getAmountPayable());

                    return bsePaymentOrderService.updateById(paymentOrder);
                }
            }

        } catch (Exception e) {
            log.error("更新订单支付状态失败，订单号：{}", orderNo, e);
        }

        return false;
    }

    /**
     * 生成退款单号
     */
    private String generateRefundNo() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        return "RF" + LocalDateTime.now().format(formatter) + IdUtil.randomUUID().substring(0, 8);
    }



    /**
     * 支付成功后更新订单
     */
    private boolean updateOrderAfterPayment(Map<String, Object> callbackData) {
        String outTradeNo = (String) callbackData.get("out_trade_no");
        BsePaymentOrder paymentOrder = bsePaymentOrderService.lambdaQuery()
            .eq(BsePaymentOrder::getOrderNo, outTradeNo)
            .one();

        if (paymentOrder == null) {
            log.error("订单不存在：{}", outTradeNo);
            return false;
        }

        // 防止重复处理
        if (WechatPayConstants.ORDER_STATUS_PAID.equals(paymentOrder.getStatus())) {
            log.info("订单已处理：{}", outTradeNo);
            return true;
        }

        paymentOrder.setStatus(WechatPayConstants.ORDER_STATUS_PAID);
        paymentOrder.setChannelOrderNo((String) callbackData.get("transaction_id"));

        // 处理金额字段，可能是String或Integer
        Object totalFeeObj = callbackData.get("total_fee");
        if (totalFeeObj != null) {
            if (totalFeeObj instanceof String) {
                paymentOrder.setAmountPaid(Integer.valueOf((String) totalFeeObj));
            } else if (totalFeeObj instanceof Integer) {
                paymentOrder.setAmountPaid((Integer) totalFeeObj);
            }
        }

        paymentOrder.setPaidAt(LocalDateTime.now());

        log.info("更新订单状态：订单号={}，微信交易号={}，金额={}",
            outTradeNo, callbackData.get("transaction_id"), totalFeeObj);

        return bsePaymentOrderService.updateById(paymentOrder);
    }

    /**
     * 调用微信退款接口 (APIv3)
     */
    private boolean callWechatRefundV3(BsePaymentOrder paymentOrder, WechatRefundDTO refundDTO, String refundNo) {
        try {
            RefundService refundService = wechatPayV3ClientFactory.getRefundService();

            // 构建退款请求
            CreateRequest request = new CreateRequest();
            request.setOutTradeNo(paymentOrder.getOrderNo());
            request.setOutRefundNo(refundNo);
            request.setReason(refundDTO.getRefundReason());
            request.setNotifyUrl(wechatPayProperties.getNotifyUrl());

            // 设置金额
            AmountReq amount = new AmountReq();
            amount.setRefund(Long.valueOf(refundDTO.getRefundAmount()));
            amount.setTotal(Long.valueOf(paymentOrder.getAmountPaid()));
            amount.setCurrency("CNY");
            request.setAmount(amount);

            // 执行退款
            Refund refund = refundService.create(request);

            if (refund != null) {
                log.info("微信退款申请成功，退款单号: {}，状态: {}", refund.getOutRefundNo(), refund.getStatus());
                return true;
            }

            return false;
        } catch (ServiceException e) {
            log.error("微信退款申请失败，错误码：{}，错误信息：{}", e.getErrorCode(), e.getErrorMessage());
            return false;
        } catch (Exception e) {
            log.error("调用微信退款接口失败", e);
            return false;
        }
    }

    /**
     * 转换微信退款状态为订单退款状态
     */
    private String convertWechatRefundStatusToOrderStatus(String wechatRefundStatus) {
        return switch (wechatRefundStatus) {
            case "SUCCESS" -> WechatPayConstants.REFUND_STATUS_SUCCESS;
            case "CLOSED" -> WechatPayConstants.REFUND_STATUS_FAILED;
            case "PROCESSING" -> WechatPayConstants.REFUND_STATUS_PENDING;
            case "ABNORMAL" -> WechatPayConstants.REFUND_STATUS_ABNORMAL;
            default -> WechatPayConstants.REFUND_STATUS_FAILED;
        };
    }
}
