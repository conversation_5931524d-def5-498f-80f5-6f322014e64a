/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.facade.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.common.util.CglibUtil;
import com.izpan.infrastructure.page.PageQuery;
import com.izpan.infrastructure.page.RPage;
import com.izpan.modules.base.domain.bo.BsePaymentOrderBO;
import com.izpan.modules.base.domain.dto.payment.order.BsePaymentOrderAddDTO;
import com.izpan.modules.base.domain.dto.payment.order.BsePaymentOrderDeleteDTO;
import com.izpan.modules.base.domain.dto.payment.order.BsePaymentOrderSearchDTO;
import com.izpan.modules.base.domain.dto.payment.order.BsePaymentOrderUpdateDTO;
import com.izpan.modules.base.domain.entity.BsePaymentOrder;
import com.izpan.modules.base.domain.vo.BsePaymentOrderVO;
import com.izpan.modules.base.facade.IBsePaymentOrderFacade;
import com.izpan.modules.base.service.IBsePaymentOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import lombok.NonNull;
import org.springframework.transaction.annotation.Transactional;

/**
 * 基础服务-支付与退款订单表 门面接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName  com.izpan.modules.base.facade.impl.BsePaymentOrderFacadeImpl
 * @CreateTime 2025-07-20 - 18:05:44
 */

@Service
@RequiredArgsConstructor
public class BsePaymentOrderFacadeImpl implements IBsePaymentOrderFacade {

    @NonNull
    private IBsePaymentOrderService bsePaymentOrderService;

    @Override
    public RPage<BsePaymentOrderVO> listBsePaymentOrderPage(PageQuery pageQuery, BsePaymentOrderSearchDTO bsePaymentOrderSearchDTO) {
        BsePaymentOrderBO bsePaymentOrderBO = CglibUtil.convertObj(bsePaymentOrderSearchDTO, BsePaymentOrderBO::new);
        IPage<BsePaymentOrder> bsePaymentOrderIPage = bsePaymentOrderService.listBsePaymentOrderPage(pageQuery, bsePaymentOrderBO);
        return RPage.build(bsePaymentOrderIPage, BsePaymentOrderVO::new);
    }

    @Override
    public BsePaymentOrderVO get(Long id) {
        BsePaymentOrder byId = bsePaymentOrderService.getById(id);
        return CglibUtil.convertObj(byId, BsePaymentOrderVO::new);
    }

    @Override
    @Transactional
    public boolean add(BsePaymentOrderAddDTO bsePaymentOrderAddDTO) {
        BsePaymentOrderBO bsePaymentOrderBO = CglibUtil.convertObj(bsePaymentOrderAddDTO, BsePaymentOrderBO::new);
        return bsePaymentOrderService.save(bsePaymentOrderBO);
    }

    @Override
    @Transactional
    public boolean update(BsePaymentOrderUpdateDTO bsePaymentOrderUpdateDTO) {
        BsePaymentOrderBO bsePaymentOrderBO = CglibUtil.convertObj(bsePaymentOrderUpdateDTO, BsePaymentOrderBO::new);
        return bsePaymentOrderService.updateById(bsePaymentOrderBO);
    }

    @Override
    @Transactional
    public boolean batchDelete(BsePaymentOrderDeleteDTO bsePaymentOrderDeleteDTO) {
        BsePaymentOrderBO bsePaymentOrderBO = CglibUtil.convertObj(bsePaymentOrderDeleteDTO, BsePaymentOrderBO::new);
        return bsePaymentOrderService.removeBatchByIds(bsePaymentOrderBO.getIds(), true);
    }

}