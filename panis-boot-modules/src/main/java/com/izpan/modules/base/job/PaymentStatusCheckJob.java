/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.izpan.infrastructure.constants.WechatPayConstants;
import com.izpan.modules.base.domain.entity.BsePaymentOrder;
import com.izpan.modules.base.service.IBsePaymentOrderService;
import com.izpan.modules.base.service.IWechatPayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 支付状态检查定时任务
 * 定期查询待支付订单的支付状态，作为回调通知的补充机制
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.job.PaymentStatusCheckJob
 * @CreateTime 2025-07-20
 */
@Slf4j
@Component
@RequiredArgsConstructor
@DisallowConcurrentExecution // 不允许并发执行
public class PaymentStatusCheckJob extends QuartzJobBean {

    private final IBsePaymentOrderService bsePaymentOrderService;
    private final IWechatPayService wechatPayService;

    @Override
    protected void executeInternal(JobExecutionContext context) {
        log.info("开始执行支付状态检查定时任务");
        
        try {
            // 查询待支付的订单（创建时间在30分钟内，避免查询过期订单）
            LocalDateTime thirtyMinutesAgo = LocalDateTime.now().minusMinutes(30);
            
            LambdaQueryWrapper<BsePaymentOrder> queryWrapper = new LambdaQueryWrapper<BsePaymentOrder>()
                .eq(BsePaymentOrder::getStatus, WechatPayConstants.ORDER_STATUS_PENDING)
                .eq(BsePaymentOrder::getPaymentChannel, WechatPayConstants.PAYMENT_CHANNEL_WECHAT_LITE)
                .ge(BsePaymentOrder::getCreateTime, thirtyMinutesAgo)
                .orderByAsc(BsePaymentOrder::getCreateTime);
            
            List<BsePaymentOrder> pendingOrders = bsePaymentOrderService.list(queryWrapper);
            
            if (pendingOrders.isEmpty()) {
                log.info("没有待检查的支付订单");
                return;
            }
            
            log.info("找到 {} 个待检查的支付订单", pendingOrders.size());
            
            int successCount = 0;
            int failCount = 0;
            
            // 逐个检查订单状态
            for (BsePaymentOrder order : pendingOrders) {
                try {
                    boolean updated = wechatPayService.updateOrderPayStatus(order.getOrderNo());
                    if (updated) {
                        successCount++;
                        log.info("订单状态更新成功：{}", order.getOrderNo());
                    } else {
                        failCount++;
                    }
                    
                    // 避免频繁调用微信接口，每次查询间隔100ms
                    Thread.sleep(100);
                    
                } catch (Exception e) {
                    failCount++;
                    log.error("检查订单状态失败：{}", order.getOrderNo(), e);
                }
            }
            
            log.info("支付状态检查完成，成功更新：{}，失败：{}", successCount, failCount);
            
        } catch (Exception e) {
            log.error("执行支付状态检查定时任务失败", e);
        }
    }

}
