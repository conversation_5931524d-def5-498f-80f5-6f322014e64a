<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.izpan</groupId>
        <artifactId>panis-boot</artifactId>
        <version>1.0.5-SNAPSHOT</version>
    </parent>

    <version>1.0.5-SNAPSHOT</version>
    <name>panis-boot-modules</name>
    <artifactId>panis-boot-modules</artifactId>
    <description>PanisBoot 后台管理系统 - 业务模块</description>

    <dependencies>
        <!-- Infrastructure -->
        <dependency>
            <groupId>com.izpan</groupId>
            <artifactId>panis-boot-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.13</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.18.0</version>
        </dependency>
        <dependency>
            <groupId>com.izpan</groupId>
            <artifactId>panis-boot-common</artifactId>
        </dependency>

        <!-- fastjson2 -->
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>

        <!-- 微信支付V3 SDK -->
        <dependency>
            <groupId>com.github.wechatpay-apiv3</groupId>
            <artifactId>wechatpay-java</artifactId>
        </dependency>

        <!-- Google Gson -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>

    </dependencies>

</project>