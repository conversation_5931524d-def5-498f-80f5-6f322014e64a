/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.infrastructure.config;

import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import com.wechat.pay.java.service.refund.RefundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.io.FileNotFoundException;

/**
 * 微信支付 APIv3 客户端工厂
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.infrastructure.config.WechatPayV3ClientFactory
 * @CreateTime 2025-07-21
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WechatPayV3ClientFactory {

    private final WechatPayProperties wechatPayProperties;

    private volatile Config config;
    private volatile JsapiService jsapiService;
    private volatile RefundService refundService;

    /**
     * 获取微信支付配置
     *
     * @return Config
     */
    public Config getConfig() {
        if (config == null) {
            synchronized (WechatPayV3ClientFactory.class) {
                if (config == null) {
                    try {
                        config = buildConfig();
                    } catch (Exception e) {
                        log.error("初始化微信支付配置失败", e);
                        throw new RuntimeException("初始化微信支付配置失败", e);
                    }
                }
            }
        }
        return config;
    }

    /**
     * 获取 JSAPI 支付服务
     *
     * @return JsapiService
     */
    public JsapiService getJsapiService() {
        if (jsapiService == null) {
            synchronized (WechatPayV3ClientFactory.class) {
                if (jsapiService == null) {
                    jsapiService = new JsapiService.Builder().config(getConfig()).build();
                }
            }
        }
        return jsapiService;
    }

    /**
     * 获取退款服务
     *
     * @return RefundService
     */
    public RefundService getRefundService() {
        if (refundService == null) {
            synchronized (WechatPayV3ClientFactory.class) {
                if (refundService == null) {
                    refundService = new RefundService.Builder().config(getConfig()).build();
                }
            }
        }
        return refundService;
    }

    /**
     * 构建微信支付配置
     */
    private Config buildConfig() throws FileNotFoundException {
        return new RSAAutoCertificateConfig.Builder()
                .merchantId(wechatPayProperties.getMchId())
                .privateKeyFromPath(wechatPayProperties.getPrivateKeyPath())
                .merchantSerialNumber(wechatPayProperties.getMerchantSerialNumber())
                .apiV3Key(wechatPayProperties.getApiV3Key())
                .build();
    }
}
