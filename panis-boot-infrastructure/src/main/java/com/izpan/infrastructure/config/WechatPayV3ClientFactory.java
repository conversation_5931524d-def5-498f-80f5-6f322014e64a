/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.infrastructure.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;

/**
 * 微信支付 APIv3 客户端工厂
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.infrastructure.config.WechatPayV3ClientFactory
 * @CreateTime 2025-07-21
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WechatPayV3ClientFactory {

    private final WechatPayProperties wechatPayProperties;
    
    private volatile CloseableHttpClient httpClient;
    private volatile ScheduledUpdateCertificatesVerifier verifier;

    /**
     * 获取微信支付 HttpClient 实例
     * 
     * @return CloseableHttpClient
     */
    public CloseableHttpClient getHttpClient() {
        if (httpClient == null) {
            synchronized (WechatPayV3ClientFactory.class) {
                if (httpClient == null) {
                    try {
                        httpClient = buildHttpClient();
                    } catch (Exception e) {
                        log.error("初始化微信支付客户端失败", e);
                        throw new RuntimeException("初始化微信支付客户端失败", e);
                    }
                }
            }
        }
        return httpClient;
    }
    
    /**
     * 获取证书验证器
     * 
     * @return ScheduledUpdateCertificatesVerifier
     */
    public ScheduledUpdateCertificatesVerifier getVerifier() {
        if (verifier == null) {
            synchronized (WechatPayV3ClientFactory.class) {
                if (verifier == null) {
                    try {
                        verifier = buildVerifier();
                    } catch (Exception e) {
                        log.error("初始化微信支付证书验证器失败", e);
                        throw new RuntimeException("初始化微信支付证书验证器失败", e);
                    }
                }
            }
        }
        return verifier;
    }

    /**
     * 构建 HttpClient
     */
    private CloseableHttpClient buildHttpClient() throws FileNotFoundException {
        // 加载商户私钥
        PrivateKey merchantPrivateKey = PemUtil.loadPrivateKey(
            new FileInputStream(wechatPayProperties.getPrivateKeyPath())
        );

        // 创建定时更新平台证书的验签器
        ScheduledUpdateCertificatesVerifier verifier = buildVerifier();

        // 构建HttpClient
        return WechatPayHttpClientBuilder.create()
                .withMerchant(
                    wechatPayProperties.getMchId(), 
                    wechatPayProperties.getMerchantSerialNumber(), 
                    merchantPrivateKey
                )
                .withValidator(new WechatPay2Validator(verifier))
                .build();
    }
    
    /**
     * 构建证书验证器
     */
    private ScheduledUpdateCertificatesVerifier buildVerifier() throws FileNotFoundException {
        // 加载商户私钥
        PrivateKey merchantPrivateKey = PemUtil.loadPrivateKey(
            new FileInputStream(wechatPayProperties.getPrivateKeyPath())
        );

        // 创建定时更新平台证书的验签器
        return new ScheduledUpdateCertificatesVerifier(
                new WechatPay2Credentials(
                    wechatPayProperties.getMchId(), 
                    new PrivateKeySigner(
                        wechatPayProperties.getMerchantSerialNumber(), 
                        merchantPrivateKey
                    )
                ),
                wechatPayProperties.getApiV3Key().getBytes(StandardCharsets.UTF_8)
        );
    }
}
