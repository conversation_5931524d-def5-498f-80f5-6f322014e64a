/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.infrastructure.constants;

/**
 * 微信支付常量类
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.infrastructure.constants.WechatPayConstants
 * @CreateTime 2025-07-20
 */
public class WechatPayConstants {

    /**
     * 微信支付接口地址
     */
    public static final String UNIFIED_ORDER_URL = "https://api.mch.weixin.qq.com/pay/unifiedorder";
    public static final String ORDER_QUERY_URL = "https://api.mch.weixin.qq.com/pay/orderquery";
    public static final String REFUND_URL = "https://api.mch.weixin.qq.com/secapi/pay/refund";
    public static final String REFUND_QUERY_URL = "https://api.mch.weixin.qq.com/pay/refundquery";

    /**
     * 交易类型
     */
    public static final String TRADE_TYPE_JSAPI = "JSAPI";

    /**
     * 支付渠道编码
     */
    public static final String PAYMENT_CHANNEL_WECHAT_LITE = "WECHAT_LITE";

    /**
     * 订单状态
     */
    public static final String ORDER_STATUS_PENDING = "PENDING_PAYMENT";
    public static final String ORDER_STATUS_PAID = "PAID";
    public static final String ORDER_STATUS_CLOSED = "CLOSED";
    public static final String ORDER_STATUS_FAILED = "FAILED";

    /**
     * 退款状态
     */
    public static final String REFUND_STATUS_NO_REFUND = "NO_REFUND";
    public static final String REFUND_STATUS_PENDING = "REFUND_PENDING";
    public static final String REFUND_STATUS_SUCCESS = "REFUND_SUCCESS";
    public static final String REFUND_STATUS_FAILED = "REFUND_FAILED";

    /**
     * 微信支付结果状态
     */
    public static final String WECHAT_PAY_SUCCESS = "SUCCESS";
    public static final String WECHAT_PAY_FAIL = "FAIL";

    /**
     * 微信支付回调返回状态
     */
    public static final String WECHAT_CALLBACK_SUCCESS = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
    public static final String WECHAT_CALLBACK_FAIL = "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[签名失败]]></return_msg></xml>";

    /**
     * 签名类型
     */
    public static final String SIGN_TYPE_MD5 = "MD5";

    /**
     * 字符编码
     */
    public static final String CHARSET_UTF8 = "UTF-8";

}
