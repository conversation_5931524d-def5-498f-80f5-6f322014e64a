package com.izpan.admin.controller.system;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.izpan.admin.config.TaskSubmitter;
import com.izpan.modules.biz.domain.dto.composition.log.BizCompositionLogAddDTO;
import com.izpan.modules.biz.domain.entity.*;
import com.izpan.modules.biz.facade.IBizCompositionLogFacade;
import com.izpan.modules.system.domain.dto.user.SysUserAddDTO;
import com.izpan.modules.system.facade.ISysUserFacade;
import com.izpan.modules.tools.service.RemoteDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Semaphore;

/**
 * 同步学生数据控制器（并发安全改造版）
 */
@RestController
@RequestMapping("/sync-student-data")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "同步学生数据")
public class SyncStudentDataController {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy/M/d H:m:s");


    private static final String USER_URL = "http://hyzy.cewenwang.com/interface/Data_School.asmx/GetUser";
    private static final String WRITING_URL = "http://hyzy.cewenwang.com/interface/Data_School.asmx/selectzuowen_list?uid=%s&schoolid=0";

    // 作文日志映射缓存（线程安全）
    private static final Map<String, Long> COMPOSITION_LOG_MAP = new ConcurrentHashMap<>();


    @NonNull
    private final RemoteDataService remoteDataService;

    @NonNull
    private final ISysUserFacade sysUserFacade;

    @NonNull
    private final IBizCompositionLogFacade bizCompositionLogFacade;

    @Resource(name = "dataSyncExecutor") // 自定义线程池
    private ExecutorService executor;

    @PostConstruct
    public void initTaskSubmitter() {
        studentTaskSubmitter = new TaskSubmitter(executor, new Semaphore(3));
    }

    private TaskSubmitter studentTaskSubmitter;



    @GetMapping("/sync")
    @Operation(operationId = "3", summary = "同步学生")
    public void syncStudent() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("开始同步学生");
        JSONArray studentList = remoteDataService.fetchJsonArray(USER_URL, "usreList");
        processStudents(studentList);
        COMPOSITION_LOG_MAP.clear();
        stopWatch.stop();
        log.info("同步学生完成，耗时：{} s", stopWatch.getTotalTimeSeconds());
        
    }

    private void processStudents(JSONArray students) {
        if (students == null || students.isEmpty()) return;

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (int i = 0; i < students.size(); i++) {
            JSONObject studentObj = students.getJSONObject(i);
            futures.add(studentTaskSubmitter.submit(() -> processStudent(studentObj)));
        }

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    private void processStudent(JSONObject studentObj) {
        Pair<SysUserAddDTO, Long> pair = saveStudent(studentObj);
        String userId = studentObj.getString("userId");

        JSONArray writings = remoteDataService.fetchJsonArray(String.format(WRITING_URL, userId), "CpList");
        processWritings(writings, pair.getLeft(), pair.getRight());
    }

    /**
     * 同步处理作文
     */
    private void processWritings(JSONArray writings, SysUserAddDTO sysUserAddDTO, Long userId) {
        if (writings == null || writings.isEmpty()) return;

        for (int m = 0; m < writings.size(); m++) {
            JSONObject writingObj = writings.getJSONObject(m);
            BizCompositionLogAddDTO dto = convertToBizCompositionLogBO(writingObj, sysUserAddDTO, userId);
            bizCompositionLogFacade.syncAdd(dto);
        }
    }

    private Pair<SysUserAddDTO, Long> saveStudent(JSONObject obj) {
        SysUserAddDTO dto = convertToStudentSysUserBO(obj);
        return Pair.of(dto, sysUserFacade.addUser(dto));
    }

    private SysUserAddDTO convertToStudentSysUserBO(JSONObject item) {
        SysUserAddDTO dto = new SysUserAddDTO();
        dto.setMappingId(item.getLong("userId"));
        dto.setUserName(item.getString("userName"));
        dto.setRealName(item.getString("realName"));
        dto.setNickName(item.getString("realName"));
        dto.setPhone(item.getString("phone"));
        dto.setUserType("5");
        dto.setMicroLessonsNumber(item.getInteger("microLessonsNumber"));
        dto.setEvaluationNumber(item.getInteger("evaluationNumber"));
        dto.setStatus("1");
        dto.setPassword(item.getString("password"));
        return dto;
    }

    private BizCompositionLogAddDTO convertToBizCompositionLogBO(JSONObject item, SysUserAddDTO sysUserAddDTO, Long userId) {
        BizCompositionLogAddDTO log = new BizCompositionLogAddDTO();
        Long initId = item.getLong("initId");
        Long mappingId = item.getLong("Id");
        if (ObjectUtil.equal(initId,0L)) {
            initId = mappingId;
        }
        String key = userId + ":" + initId;
        COMPOSITION_LOG_MAP.computeIfAbsent(key, k -> IdUtil.getSnowflakeNextId());
        if (ObjectUtil.equal(initId,mappingId)) {
            log.setId(COMPOSITION_LOG_MAP.get(key));
        }else {
            log.setId(IdUtil.getSnowflakeNextId());
        }
        log.setTitle(item.getString("Title"));
        log.setRequireTitle(item.getString("TitleLimit"));
        log.setRequireWords(item.getInteger("WordCountLimit"));
        log.setRequireWenTi(item.getString("StyleLimit"));
        log.setWenTi(item.getString("Style"));
        log.setScore(item.getDouble("score"));
        log.setPercentage(item.getDouble("Percentage"));
        log.setComments(item.getString("Comments"));
        log.setParagraphs(item.getInteger("Paragraphcount"));
        log.setWords(item.getInteger("Wordcount"));
        log.setSentences(item.getInteger("Sentencecount"));
        log.setFullScore(item.getDouble("Sfullmarks"));
        log.setClassified(item.getString("Classified"));
        log.setSensitive(item.getString("Sensitive"));
        log.setStar(item.getInteger("Star"));
        log.setInitLogId(COMPOSITION_LOG_MAP.get(key));
        log.setMappingId(mappingId);
        log.setSubmitTime(LocalDateTime.parse(item.getString("createTime"), FORMATTER));
        log.setSource("2");
        log.setEvaluateSources(item.getInteger("evaluateSources"));
        log.setAnswerStatus(item.getInteger("answerStatus"));
        log.setAuthor(item.getString("author"));

        log.setAnswerId(userId);
        log.setAnswerName(sysUserAddDTO.getRealName());

        log.setContentList(convertToList(item.getJSONArray("ContentList"), BizCompositionContent.class));
        log.setErrorList(convertToList(item.getJSONArray("ErrorList"), BizCompositionErrorLog.class));
        log.setJiList(convertToList(item.getJSONArray("JiList"), BizCompositionJi.class));
        log.setMeiList(convertToList(item.getJSONArray("MeiList"), BizCompositionMeipi.class));
        log.setCtList(convertToList(item.getJSONArray("CTList"), BizCompositionCt.class));

        return log;
    }

    private <T> List<T> convertToList(JSONArray array, Class<T> clazz) {
        if (array == null || array.isEmpty()) return Collections.emptyList();
        return array.toJavaList(clazz);
    }


}
