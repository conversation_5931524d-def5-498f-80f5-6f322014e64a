/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.base;

import com.izpan.infrastructure.config.WechatPayProperties;
import com.izpan.infrastructure.config.WechatPayV3ClientFactory;
import com.wechat.pay.contrib.apache.httpclient.notification.Notification;
import com.wechat.pay.contrib.apache.httpclient.notification.NotificationHandler;
import com.wechat.pay.contrib.apache.httpclient.notification.NotificationRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信支付回调控制器
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.base.WechatPayCallbackController
 * @CreateTime 2025-07-21
 */
@Slf4j
@RestController
@Tag(name = "微信支付回调")
@RequiredArgsConstructor
@RequestMapping("wechat/pay")
public class WechatPayCallbackController {

    private final WechatPayProperties wechatPayProperties;
    private final WechatPayV3ClientFactory wechatPayV3ClientFactory;

    /**
     * 处理微信支付回调通知
     */
    @PostMapping("/notify")
    @Operation(operationId = "1", summary = "微信支付回调通知")
    public Map<String, String> handlePayNotify(HttpServletRequest request, @RequestBody String requestBody) {
        Map<String, String> response = new HashMap<>();
        
        try {
            // 构造通知请求
            NotificationRequest notificationRequest = new NotificationRequest.Builder()
                    .withSerialNumber(request.getHeader("Wechatpay-Serial"))
                    .withNonce(request.getHeader("Wechatpay-Nonce"))
                    .withTimestamp(request.getHeader("Wechatpay-Timestamp"))
                    .withSignature(request.getHeader("Wechatpay-Signature"))
                    .withBody(requestBody)
                    .build();

            // 获取验证器
            NotificationHandler handler = new NotificationHandler(
                    wechatPayV3ClientFactory.getVerifier(),
                    wechatPayProperties.getApiV3Key().getBytes(StandardCharsets.UTF_8)
            );
            
            // 验签和解密
            Notification notification = handler.parse(notificationRequest);
            
            // 获取解密后的数据
            String decryptData = notification.getDecryptData();
            log.info("微信支付回调通知解密数据: {}", decryptData);
            
            // 根据事件类型处理业务逻辑
            String eventType = notification.getEventType();
            log.info("微信支付回调事件类型: {}", eventType);
            
            // 处理不同类型的通知
            switch (eventType) {
                case "TRANSACTION.SUCCESS":
                    // 处理支付成功通知
                    handlePaymentSuccess(decryptData);
                    break;
                case "REFUND.SUCCESS":
                    // 处理退款成功通知
                    handleRefundSuccess(decryptData);
                    break;
                case "REFUND.ABNORMAL":
                    // 处理退款异常通知
                    handleRefundAbnormal(decryptData);
                    break;
                case "REFUND.CLOSED":
                    // 处理退款关闭通知
                    handleRefundClosed(decryptData);
                    break;
                default:
                    log.warn("未知的事件类型: {}", eventType);
            }
            
            // 返回成功
            response.put("code", "SUCCESS");
            response.put("message", "成功");
            
        } catch (Exception e) {
            log.error("处理微信支付回调通知失败", e);
            response.put("code", "FAIL");
            response.put("message", "处理失败");
        }
        
        return response;
    }
    
    /**
     * 处理支付成功通知
     */
    private void handlePaymentSuccess(String decryptData) {
        // TODO: 实现支付成功的业务逻辑
        // 1. 解析 JSON 数据
        // 2. 根据商户订单号查询订单
        // 3. 更新订单状态为已支付
        log.info("处理支付成功通知: {}", decryptData);
    }
    
    /**
     * 处理退款成功通知
     */
    private void handleRefundSuccess(String decryptData) {
        // TODO: 实现退款成功的业务逻辑
        // 1. 解析 JSON 数据
        // 2. 根据商户退款单号查询退款单
        // 3. 更新退款单状态为已退款
        log.info("处理退款成功通知: {}", decryptData);
    }
    
    /**
     * 处理退款异常通知
     */
    private void handleRefundAbnormal(String decryptData) {
        // TODO: 实现退款异常的业务逻辑
        log.info("处理退款异常通知: {}", decryptData);
    }
    
    /**
     * 处理退款关闭通知
     */
    private void handleRefundClosed(String decryptData) {
        // TODO: 实现退款关闭的业务逻辑
        log.info("处理退款关闭通知: {}", decryptData);
    }
}
