/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.base;

import com.izpan.infrastructure.config.WechatPayProperties;
import com.izpan.infrastructure.config.WechatPayV3ClientFactory;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.refund.model.RefundNotification;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信支付回调控制器
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.base.WechatPayCallbackController
 * @CreateTime 2025-07-21
 */
@Slf4j
@RestController
@Tag(name = "微信支付回调")
@RequiredArgsConstructor
@RequestMapping("wechat/pay")
public class WechatPayCallbackController {

    private final WechatPayProperties wechatPayProperties;
    private final WechatPayV3ClientFactory wechatPayV3ClientFactory;

    /**
     * 处理微信支付回调通知
     */
    @PostMapping("/notify")
    @Operation(operationId = "1", summary = "微信支付回调通知")
    public Map<String, String> handlePayNotify(HttpServletRequest request, @RequestBody String requestBody) {
        Map<String, String> response = new HashMap<>();

        try {
            Config config = wechatPayV3ClientFactory.getConfig();
            NotificationParser parser = new NotificationParser((com.wechat.pay.java.core.notification.NotificationConfig) config);

            // 构造请求参数
            RequestParam requestParam = new RequestParam.Builder()
                    .serialNumber(request.getHeader("Wechatpay-Serial"))
                    .nonce(request.getHeader("Wechatpay-Nonce"))
                    .timestamp(request.getHeader("Wechatpay-Timestamp"))
                    .signature(request.getHeader("Wechatpay-Signature"))
                    .body(requestBody)
                    .build();

            // 解析回调通知，这里需要根据实际的回调类型来解析
            // 支付回调
            if (requestBody.contains("TRANSACTION.SUCCESS")) {
                Transaction transaction = parser.parse(requestParam, Transaction.class);
                handlePaymentSuccess(transaction);
            }
            // 退款回调
            else if (requestBody.contains("REFUND.SUCCESS") || requestBody.contains("REFUND.ABNORMAL") || requestBody.contains("REFUND.CLOSED")) {
                RefundNotification refund = parser.parse(requestParam, RefundNotification.class);
                handleRefundCallback(refund);
            }

            // 返回成功
            response.put("code", "SUCCESS");
            response.put("message", "成功");

        } catch (Exception e) {
            log.error("处理微信支付回调通知失败", e);
            response.put("code", "FAIL");
            response.put("message", "处理失败");
        }

        return response;
    }
    
    /**
     * 处理支付成功通知
     */
    private void handlePaymentSuccess(Transaction transaction) {
        // TODO: 实现支付成功的业务逻辑
        // 1. 根据商户订单号查询订单
        // 2. 更新订单状态为已支付
        log.info("处理支付成功通知，订单号: {}，微信订单号: {}，状态: {}",
                transaction.getOutTradeNo(), transaction.getTransactionId(), transaction.getTradeState());
    }

    /**
     * 处理退款回调通知
     */
    private void handleRefundCallback(RefundNotification refund) {
        // TODO: 实现退款回调的业务逻辑
        // 1. 根据商户退款单号查询退款单
        // 2. 更新退款单状态
        log.info("处理退款回调通知，退款单号: {}，微信退款单号: {}，状态: {}",
                refund.getOutRefundNo(), refund.getRefundId(), refund.getRefundStatus());
    }
}
