spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************************************************************
    username: cewenwang
    password: JkbkMtStanRjRPdz
  data:
    redis:
      database: 1
      host: 127.0.0.1
      port: 6379
      connect-timeout: 10000ms
      lettuce:
        pool:
          enabled: true
oss:
  name: minio
  bucket-name: cww
  endpoint: http://127.0.0.1:9000
  access-key: AKMBfHO0BN0HEP8T0Q7Z
  secret-key: 8H6tSwSv4C3KwNHaBePA43sZEVlcZYImE75SOHn3
  sync-delete: true
  expiry: 3600
  image-url: http://**************:9000/cww/

ceping:
  pocr: http://dmbth.cewenwang.com/interface/Ocr_Service.asmx/ocrcp
  pcp: http://dmbth.cewenwang.com/interface/Ceping_Servpice.asmx/submitTest_ceping_bzk
  ocr: https://jxlst.cewenwang.com/interface/Ocr_Service.asmx/ocrcp
  cp: https://jxlst.cewenwang.com/interface/Ceping_Servpice.asmx/submitTest_ceping_bzk
  fw: https://jxlst.cewenwang.com/interface/Sucai_Service.asmx/selectOne_fanwenBytitle
  sc: https://jxlst.cewenwang.com/interface/Sucai_Service.asmx/selectList_h
  dsc: https://jxlst.cewenwang.com/interface/Sucai_Service.asmx/selectOne_SucaiById

# 微信支付配置
wechat:
  pay:
    # 小程序AppID
    app-id: wx17adbb00eb04dbde
    # 商户号
    mch-id: 1457388702
    # 商户API密钥
    api-key: your_api_key
    # 支付回调通知地址
    notify-url: http://your-domain.com/wechat/pay/notify
    # 商户证书路径（退款时需要）
    cert-path: classpath:cert/apiclient_cert.p12
    # 证书密码（默认为商户号）
    cert-password: 1457388702
