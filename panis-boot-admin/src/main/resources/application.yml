server:
  # 服务端口
  port: 9999

spring:
  profiles:
    # 激活环境
    active: @env@
  application:
    name: panis-boot-admin
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  config:
    import:
      - classpath:config/druid.yml
      - classpath:config/mybatis-plus.yml
      - classpath:config/swagger.yml
      - classpath:config/quartz.yml
  servlet:
    multipart:
      enabled: true #默认支持文件上传
      max-file-size: 20MB # 最大支持文件大小
      max-request-size: 20MB # 最大支持请求大小

oss:
  name: minio
  bucket-name: cww
  endpoint: http://**************:9000
  access-key: AKMBfHO0BN0HEP8T0Q7Z
  secret-key: 8H6tSwSv4C3KwNHaBePA43sZEVlcZYImE75SOHn3
  sync-delete: true
  expiry: 3600
  image-url: http://**************:9000/cww/

ceping:
  pocr: http://dmbth.cewenwang.com/interface/Ocr_Service.asmx/ocrcp
  pcp: http://dmbth.cewenwang.com/interface/Ceping_Servpice.asmx/submitTest_ceping_bzk
  ocr: https://jxlst.cewenwang.com/interface/Ocr_Service.asmx/ocrcp
  cp: https://jxlst.cewenwang.com/interface/Ceping_Servpice.asmx/submitTest_ceping_bzk
  fw: https://jxlst.cewenwang.com/interface/Sucai_Service.asmx/selectOne_fanwenBytitle
  sc: https://jxlst.cewenwang.com/interface/Sucai_Service.asmx/selectList_h
  dsc: https://jxlst.cewenwang.com/interface/Sucai_Service.asmx/selectOne_SucaiById

# 微信配置
wechat:
  # 小程序配置
  miniprogram:
    # 小程序AppID
    app-id: wx17adbb00eb04dbde
    # 小程序AppSecret
    app-secret: 4289e801921592e0dc813aa6646eb0e7
  # 支付配置
  pay:
    # 小程序AppID
    app-id: wx17adbb00eb04dbde
    # 商户号
    mch-id: 1457388702
    # 商户API密钥（V2版本）
    api-key: 51DEFCF72D4BCE13252B786063CE57E3
    # 支付回调通知地址
    notify-url: https://www.gaibiyou.com/cww-api/wechat/pay/notify
    # 商户证书路径（退款时需要）
    cert-path: classpath:cert/apiclient_cert.p12
    # 证书密码（默认为商户号）
    cert-password: 1457388702

    # ========== V3版本配置 ==========
    # 商户API私钥路径（需要生成并放置在resources/cert目录下）
    private-key-path: classpath:cert/apiclient_key.pem
    # 商户证书序列号（需要从微信商户平台获取）
    merchant-serial-number: 44D2DD54CF83C671EF19421DE37EC295DB9A40CF
    # 商户APIV3密钥（需要在微信商户平台设置）
    api-v3-key: iI1yF3gP3iG3m19rH2rB4wG0g23uY8mQ